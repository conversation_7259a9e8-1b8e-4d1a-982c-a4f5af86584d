class Video {
  final String id;
  final String title;
  final String description;
  final String telegramVideoUrl;
  final String? thumbnailUrl;
  final DateTime createdAt;

  Video({
    required this.id,
    required this.title,
    required this.description,
    required this.telegramVideoUrl,
    this.thumbnailUrl,
    required this.createdAt,
  });

  // تحويل من JSON
  factory Video.fromJson(Map<String, dynamic> json) {
    return Video(
      id: json['id'],
      title: json['title'],
      description: json['description'] ?? '',
      telegramVideoUrl: json['telegramVideoUrl'],
      thumbnailUrl: json['thumbnailUrl'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'telegramVideoUrl': telegramVideoUrl,
      'thumbnailUrl': thumbnailUrl,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // التحقق من صحة رابط Telegram
  bool get isValidTelegramUrl {
    return telegramVideoUrl.contains('t.me') ||
        telegramVideoUrl.contains('telegram.org') ||
        telegramVideoUrl.startsWith('http');
  }

  @override
  String toString() {
    return 'Video{id: $id, title: $title, telegramVideoUrl: $telegramVideoUrl, createdAt: $createdAt}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Video && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}
