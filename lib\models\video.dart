class Video {
  final String id;
  final String title;
  final String youtubeUrl;
  final DateTime createdAt;

  Video({
    required this.id,
    required this.title,
    required this.youtubeUrl,
    required this.createdAt,
  });

  // تحويل من JSON
  factory Video.fromJson(Map<String, dynamic> json) {
    return Video(
      id: json['id'],
      title: json['title'],
      youtubeUrl: json['youtubeUrl'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'youtubeUrl': youtubeUrl,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // استخراج معرف الفيديو من رابط YouTube
  String? get youtubeVideoId {
    final regex = RegExp(
      r'(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})',
      caseSensitive: false,
    );
    final match = regex.firstMatch(youtubeUrl);
    return match?.group(1);
  }

  // التحقق من صحة رابط YouTube
  bool get isValidYouTubeUrl {
    return youtubeVideoId != null;
  }

  @override
  String toString() {
    return 'Video{id: $id, title: $title, youtubeUrl: $youtubeUrl, createdAt: $createdAt}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Video &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
