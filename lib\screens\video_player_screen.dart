import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:http/http.dart' as http;
import '../models/video.dart';

class VideoPlayerScreen extends StatefulWidget {
  final Video video;

  const VideoPlayerScreen({super.key, required this.video});

  @override
  State<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
  VideoPlayerController? _videoController;
  ChewieController? _chewieController;
  bool _isLoading = true;
  bool _hasError = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  void _initializePlayer() async {
    final videoUrl = widget.video.telegramVideoUrl;

    if (videoUrl.isEmpty) {
      setState(() {
        _hasError = true;
        _errorMessage = 'رابط الفيديو غير صحيح';
        _isLoading = false;
      });
      return;
    }

    try {
      // محاولة الحصول على الرابط المباشر للفيديو
      final directUrl = await _getDirectVideoUrl(videoUrl);

      if (directUrl == null) {
        throw Exception('لا يمكن العثور على رابط الفيديو المباشر');
      }

      // إنشاء مشغل الفيديو
      _videoController = VideoPlayerController.networkUrl(Uri.parse(directUrl));

      await _videoController!.initialize();

      // إنشاء Chewie controller للتحكم المتقدم
      _chewieController = ChewieController(
        videoPlayerController: _videoController!,
        autoPlay: false,
        looping: false,
        allowFullScreen: true,
        allowMuting: true,
        allowPlaybackSpeedChanging: true,
        showControls: true,
        materialProgressColors: ChewieProgressColors(
          playedColor: Colors.deepPurple,
          handleColor: Colors.deepPurple,
          backgroundColor: Colors.grey,
          bufferedColor: Colors.deepPurple.withValues(alpha: 0.3),
        ),
        placeholder: Container(
          color: Colors.black,
          child: const Center(
            child: CircularProgressIndicator(color: Colors.deepPurple),
          ),
        ),
        errorBuilder: (context, errorMessage) {
          return Container(
            color: Colors.black,
            child: Center(
              child: Text(
                'خطأ في تشغيل الفيديو',
                style: const TextStyle(color: Colors.white),
              ),
            ),
          );
        },
      );

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'خطأ في تحميل الفيديو: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  Future<String?> _getDirectVideoUrl(String url) async {
    try {
      // إذا كان الرابط مباشراً للفيديو، استخدمه كما هو
      if (url.endsWith('.mp4') ||
          url.endsWith('.mov') ||
          url.endsWith('.avi')) {
        return url;
      }

      // محاولة الحصول على الرابط المباشر من Telegram
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        // البحث عن رابط الفيديو في HTML
        final html = response.body;

        // البحث عن روابط الفيديو المختلفة
        final patterns = [
          RegExp(r'<video[^>]*src="([^"]*)"', caseSensitive: false),
          RegExp(r'<source[^>]*src="([^"]*)"', caseSensitive: false),
          RegExp(r'"videoUrl":"([^"]*)"', caseSensitive: false),
          RegExp(r'"url":"([^"]*\.mp4[^"]*)"', caseSensitive: false),
        ];

        for (final pattern in patterns) {
          final match = pattern.firstMatch(html);
          if (match != null) {
            String videoUrl = match.group(1)!;
            // إزالة escape characters
            videoUrl = videoUrl.replaceAll(r'\/', '/');
            videoUrl = videoUrl.replaceAll(r'\u0026', '&');
            return Uri.decodeFull(videoUrl);
          }
        }
      }

      // إذا لم نجد رابط مباشر، نحاول استخدام الرابط الأصلي
      return url;
    } catch (e) {
      debugPrint('خطأ في استخراج رابط الفيديو: $e');
      // في حالة الخطأ، نحاول استخدام الرابط الأصلي
      return url;
    }
  }

  @override
  void dispose() {
    _chewieController?.dispose();
    _videoController?.dispose();
    super.dispose();
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.video.title,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
          overflow: TextOverflow.ellipsis,
        ),
        backgroundColor: Colors.deepPurple,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [Colors.deepPurple.shade50, Colors.white],
          ),
        ),
        child: _hasError
            ? _buildErrorWidget()
            : _isLoading
            ? _buildLoadingWidget()
            : Column(
                children: [
                  // مشغل الفيديو المستقل
                  Container(
                    width: double.infinity,
                    height: 250,
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.black,
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.3),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(15),
                      child: _chewieController != null
                          ? Chewie(controller: _chewieController!)
                          : Container(
                              color: Colors.black,
                              child: const Center(
                                child: CircularProgressIndicator(
                                  color: Colors.deepPurple,
                                ),
                              ),
                            ),
                    ),
                  ),

                  // معلومات الفيديو
                  Expanded(
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(15),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 5,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildInfoRow(
                              Icons.title,
                              'العنوان',
                              widget.video.title,
                            ),

                            const SizedBox(height: 10),

                            _buildInfoRow(
                              Icons.description,
                              'الوصف',
                              widget.video.description,
                            ),

                            const SizedBox(height: 10),

                            _buildInfoRow(
                              Icons.link,
                              'رابط الفيديو',
                              widget.video.telegramVideoUrl,
                              isUrl: true,
                            ),

                            const SizedBox(height: 10),

                            _buildInfoRow(
                              Icons.calendar_today,
                              'تاريخ الإضافة',
                              _formatDate(widget.video.createdAt),
                            ),

                            const SizedBox(height: 20),

                            // ملاحظة الخصوصية
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.green.shade50,
                                borderRadius: BorderRadius.circular(10),
                                border: Border.all(
                                  color: Colors.green.shade200,
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.privacy_tip_outlined,
                                    color: Colors.green.shade600,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      'مشغل فيديو مستقل - لا توجد أي تتبع أو إعلانات',
                                      style: TextStyle(
                                        color: Colors.green.shade700,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(color: Colors.deepPurple),
          SizedBox(height: 20),
          Text(
            'جاري تحميل الفيديو...',
            style: TextStyle(fontSize: 16, color: Colors.deepPurple),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 80, color: Colors.red.shade400),
            const SizedBox(height: 20),
            Text(
              'خطأ في تحميل الفيديو',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.red.shade600,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              _errorMessage ?? 'حدث خطأ غير متوقع',
              style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 30),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pop(context);
              },
              icon: const Icon(Icons.arrow_back),
              label: const Text('العودة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 30,
                  vertical: 15,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    IconData icon,
    String label,
    String value, {
    bool isUrl = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: Colors.deepPurple.shade600),
        const SizedBox(width: 10),
        Text(
          '$label: ',
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: isUrl ? Colors.blue.shade600 : Colors.grey.shade700,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: isUrl ? 2 : 1,
          ),
        ),
      ],
    );
  }
}
