import 'package:flutter/material.dart';
import 'package:youtube_player_iframe/youtube_player_iframe.dart';
import '../models/video.dart';

class VideoPlayerScreen extends StatefulWidget {
  final Video video;

  const VideoPlayerScreen({super.key, required this.video});

  @override
  State<VideoPlayerScreen> createState() => _VideoPlayerScreenState();
}

class _VideoPlayerScreenState extends State<VideoPlayerScreen> {
  late YoutubePlayerController _controller;
  bool _hasError = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  void _initializePlayer() {
    final videoId = widget.video.youtubeVideoId;

    if (videoId == null) {
      setState(() {
        _hasError = true;
        _errorMessage = 'رابط الفيديو غير صحيح';
      });
      return;
    }

    try {
      _controller = YoutubePlayerController.fromVideoId(
        videoId: videoId,
        autoPlay: false,
        params: const YoutubePlayerParams(
          // إعدادات الخصوصية المطلوبة
          showFullscreenButton: true,
          showVideoAnnotations: false,
          enableJavaScript: true,
          strictRelatedVideos: true,

          // تمكين عناصر التحكم
          showControls: true,
          enableCaption: true,

          // إعدادات لإخفاء الشعارات
          color: 'white',

          // استخدام وضع التضمين
          playsInline: false,

          // إعدادات إضافية
          mute: false,
          loop: false,

          // إعدادات متقدمة
          interfaceLanguage: 'ar',
        ),
      );

      _controller.setFullScreenListener((isFullScreen) {
        // منع وضع الشاشة الكاملة
        if (isFullScreen) {
          _controller.exitFullScreen();
        }
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'خطأ في تحميل الفيديو: $e';
      });
    }
  }

  @override
  void dispose() {
    if (!_hasError) {
      _controller.close();
    }
    super.dispose();
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return YoutubePlayerScaffold(
      controller: _controller,
      aspectRatio: 16 / 9,
      builder: (context, player) {
        return Scaffold(
          appBar: AppBar(
            title: Text(
              widget.video.title,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              overflow: TextOverflow.ellipsis,
            ),
            backgroundColor: Colors.deepPurple,
            foregroundColor: Colors.white,
            elevation: 0,
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Colors.deepPurple.shade50, Colors.white],
              ),
            ),
            child: _hasError
                ? _buildErrorWidget()
                : Column(
                    children: [
                      // مشغل الفيديو مع طبقات إخفاء محسنة
                      Container(
                        width: double.infinity,
                        height: 250,
                        margin: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.black,
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.3),
                              blurRadius: 10,
                              offset: const Offset(0, 5),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(15),
                          child: Stack(
                            children: [
                              player,
                              // طبقة لإخفاء شعار YouTube في الزاوية اليمنى السفلى
                              Positioned(
                                bottom: 0,
                                right: 0,
                                child: Container(
                                  width: 100,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    color: Colors.black.withValues(alpha: 1.0),
                                    borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(15),
                                    ),
                                  ),
                                ),
                              ),
                              // طبقة لإخفاء زر "Watch on YouTube" في الزاوية اليسرى السفلى
                              Positioned(
                                bottom: 0,
                                left: 0,
                                child: Container(
                                  width: 160,
                                  height: 40,
                                  decoration: BoxDecoration(
                                    color: Colors.black.withValues(alpha: 1.0),
                                    borderRadius: const BorderRadius.only(
                                      topRight: Radius.circular(15),
                                    ),
                                  ),
                                ),
                              ),
                              // طبقة إضافية لإخفاء أي عناصر في الوسط السفلي
                              Positioned(
                                bottom: 0,
                                left: 160,
                                right: 100,
                                child: Container(
                                  height: 40,
                                  color: Colors.black.withValues(alpha: 1.0),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: 20),

                      // معلومات الفيديو
                      Expanded(
                        child: Container(
                          margin: const EdgeInsets.all(16),
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(15),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withValues(alpha: 0.2),
                                blurRadius: 5,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'معلومات الفيديو',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.deepPurple.shade800,
                                ),
                              ),

                              const SizedBox(height: 15),

                              _buildInfoRow(
                                Icons.title,
                                'العنوان',
                                widget.video.title,
                              ),

                              const SizedBox(height: 10),

                              _buildInfoRow(
                                Icons.calendar_today,
                                'تاريخ الإضافة',
                                _formatDate(widget.video.createdAt),
                              ),

                              const SizedBox(height: 20),

                              // ملاحظة الخصوصية
                              Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.green.shade50,
                                  borderRadius: BorderRadius.circular(10),
                                  border: Border.all(
                                    color: Colors.green.shade200,
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.privacy_tip_outlined,
                                      color: Colors.green.shade600,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        'يتم تشغيل الفيديو بوضع الخصوصية المحسن لحماية بياناتك',
                                        style: TextStyle(
                                          color: Colors.green.shade700,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
          ),
        );
      },
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 80, color: Colors.red.shade400),
            const SizedBox(height: 20),
            Text(
              'خطأ في تحميل الفيديو',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.red.shade600,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              _errorMessage ?? 'حدث خطأ غير متوقع',
              style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 30),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pop(context);
              },
              icon: const Icon(Icons.arrow_back),
              label: const Text('العودة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 30,
                  vertical: 15,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
    IconData icon,
    String label,
    String value, {
    bool isUrl = false,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: Colors.deepPurple.shade600),
        const SizedBox(width: 10),
        Text(
          '$label: ',
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14,
              color: isUrl ? Colors.blue.shade600 : Colors.grey.shade700,
            ),
            overflow: TextOverflow.ellipsis,
            maxLines: isUrl ? 2 : 1,
          ),
        ),
      ],
    );
  }
}
