import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/video.dart';

class VideoService {
  static const String _videosKey = 'saved_videos';
  static VideoService? _instance;
  
  VideoService._internal();
  
  static VideoService get instance {
    _instance ??= VideoService._internal();
    return _instance!;
  }

  // حفظ فيديو جديد
  Future<bool> saveVideo(Video video) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final videos = await getAllVideos();
      
      // التحقق من عدم وجود فيديو بنفس المعرف
      if (videos.any((v) => v.id == video.id)) {
        return false; // الفيديو موجود بالفعل
      }
      
      videos.add(video);
      final videosJson = videos.map((v) => v.toJson()).toList();
      final success = await prefs.setString(_videosKey, jsonEncode(videosJson));
      return success;
    } catch (e) {
      print('خطأ في حفظ الفيديو: $e');
      return false;
    }
  }

  // استرجاع جميع الفيديوهات
  Future<List<Video>> getAllVideos() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final videosString = prefs.getString(_videosKey);
      
      if (videosString == null || videosString.isEmpty) {
        return [];
      }
      
      final videosJson = jsonDecode(videosString) as List;
      return videosJson.map((json) => Video.fromJson(json)).toList();
    } catch (e) {
      print('خطأ في استرجاع الفيديوهات: $e');
      return [];
    }
  }

  // حذف فيديو
  Future<bool> deleteVideo(String videoId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final videos = await getAllVideos();
      
      videos.removeWhere((video) => video.id == videoId);
      
      final videosJson = videos.map((v) => v.toJson()).toList();
      final success = await prefs.setString(_videosKey, jsonEncode(videosJson));
      return success;
    } catch (e) {
      print('خطأ في حذف الفيديو: $e');
      return false;
    }
  }

  // البحث عن فيديو بالمعرف
  Future<Video?> getVideoById(String videoId) async {
    try {
      final videos = await getAllVideos();
      return videos.firstWhere(
        (video) => video.id == videoId,
        orElse: () => throw StateError('لم يتم العثور على الفيديو'),
      );
    } catch (e) {
      print('خطأ في البحث عن الفيديو: $e');
      return null;
    }
  }

  // مسح جميع الفيديوهات
  Future<bool> clearAllVideos() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_videosKey);
    } catch (e) {
      print('خطأ في مسح الفيديوهات: $e');
      return false;
    }
  }

  // عدد الفيديوهات المحفوظة
  Future<int> getVideosCount() async {
    final videos = await getAllVideos();
    return videos.length;
  }
}
